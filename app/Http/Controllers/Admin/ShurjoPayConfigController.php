<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ShurjoPayService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Inertia\Inertia;
use Inertia\Response;

class ShurjoPayConfigController extends Controller
{
    public function __construct(
        private ShurjoPayService $shurjoPayService
    ) {}

    /**
     * Show the ShurjoPay configuration form.
     */
    public function configure(): Response
    {
        $config = [
            'environment' => config('shurjopay.environment', 'sandbox'),
            'username' => config('shurjopay.username', ''),
            'password' => config('shurjopay.password', ''),
            'prefix' => config('shurjopay.prefix', ''),
            'api_url' => config('shurjopay.api_url', ''),
            'currency' => config('shurjopay.currency', 'BDT'),
            'ssl_verify' => config('shurjopay.ssl_verify', true),
            'logging_enabled' => config('shurjopay.logging.enabled', true),
            'logging_level' => config('shurjopay.logging.level', 'info'),
            'debug' => config('shurjopay.debug', false),
        ];

        // Test connection status
        $connectionStatus = $this->testConnection();

        return Inertia::render('admin/payment-gateways/shurjopay/Configure', [
            'config' => $config,
            'connection_status' => $connectionStatus,
            'supported_environments' => ['sandbox', 'production'],
            'supported_currencies' => config('shurjopay.supported_currencies', ['BDT', 'USD', 'EUR', 'GBP']),
            'api_urls' => config('shurjopay.urls', [
                'sandbox' => 'https://sandbox.shurjopayment.com',
                'production' => 'https://engine.shurjopayment.com',
            ]),
        ]);
    }

    /**
     * Store the ShurjoPay configuration.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'environment' => 'required|in:sandbox,production',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
            'prefix' => 'required|string|max:10',
            'currency' => 'required|string|in:BDT,USD,EUR,GBP',
            'ssl_verify' => 'boolean',
            'logging_enabled' => 'boolean',
            'logging_level' => 'required|in:debug,info,warning,error',
            'debug' => 'boolean',
        ]);

        try {
            // Update environment file
            $this->updateEnvironmentFile([
                'SHURJOPAY_ENVIRONMENT' => $request->environment,
                'SHURJOPAY_USERNAME' => $request->username,
                'SHURJOPAY_PASSWORD' => $request->password,
                'SHURJOPAY_PREFIX' => $request->prefix,
                'SHURJOPAY_CURRENCY' => $request->currency,
                'SHURJOPAY_SSL_VERIFY' => $request->ssl_verify ? 'true' : 'false',
                'SHURJOPAY_LOGGING_ENABLED' => $request->logging_enabled ? 'true' : 'false',
                'SHURJOPAY_LOGGING_LEVEL' => $request->logging_level,
                'SHURJOPAY_DEBUG' => $request->debug ? 'true' : 'false',
            ]);

            // Test the configuration
            $testResult = $this->testConfigurationWithCredentials(
                $request->username,
                $request->password,
                $request->environment
            );

            if ($testResult['success']) {
                Log::info('ShurjoPay configuration updated successfully', [
                    'admin_user_id' => $request->user()->id,
                    'environment' => $request->environment,
                    'username' => $request->username,
                ]);

                return redirect()->back()->with('success', 'ShurjoPay configuration saved and tested successfully!');
            } else {
                return redirect()->back()->with('warning',
                    'Configuration saved but connection test failed: ' . $testResult['message']
                );
            }

        } catch (\Exception $e) {
            Log::error('Failed to update ShurjoPay configuration', [
                'error' => $e->getMessage(),
                'admin_user_id' => $request->user()->id,
            ]);

            return redirect()->back()->with('error', 'Failed to save configuration: ' . $e->getMessage());
        }
    }

    /**
     * Test ShurjoPay connection.
     */
    public function testConnection(): array
    {
        try {
            $authData = $this->shurjoPayService->authenticate();

            if ($authData && isset($authData['token'])) {
                return [
                    'success' => true,
                    'message' => 'Connection successful',
                    'store_id' => $authData['store_id'] ?? null,
                    'token_type' => $authData['token_type'] ?? null,
                ];
            }

            return [
                'success' => false,
                'message' => 'Authentication failed - please check your credentials',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Test configuration with provided credentials.
     */
    private function testConfigurationWithCredentials(string $username, string $password, string $environment): array
    {
        try {
            $apiUrl = $environment === 'production'
                ? 'https://engine.shurjopayment.com'
                : 'https://sandbox.shurjopayment.com';

            $response = \Illuminate\Support\Facades\Http::timeout(30)
                ->post($apiUrl . '/api/get_token', [
                    'username' => $username,
                    'password' => $password,
                ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['token'])) {
                    return [
                        'success' => true,
                        'message' => 'Configuration test successful',
                        'data' => $data,
                    ];
                }
            }

            return [
                'success' => false,
                'message' => 'Invalid credentials or API response',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Update environment file with new values.
     */
    private function updateEnvironmentFile(array $values): void
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        foreach ($values as $key => $value) {
            $pattern = "/^{$key}=.*$/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);
    }
}
