APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_DEBUG_VERBOSE=false
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Plugin System Configuration
PLUGIN_SYSTEM_ENABLED=false
PLUGIN_AUTO_DISCOVERY=false
PLUGIN_SIMPLE_ENABLED=false
PLUGIN_ANALYTICS_ENABLED=false

# Paddle Payment Gateway Configuration
PADDLE_ENVIRONMENT=sandbox
PADDLE_API_KEY=
PADDLE_CLIENT_TOKEN=
PADDLE_WEBHOOK_SECRET=
PADDLE_WEBHOOK_URL=
PADDLE_VENDOR_ID=
PADDLE_CURRENCY=USD
PADDLE_LOGGING_ENABLED=true
PADDLE_LOGGING_CHANNEL=daily
PADDLE_LOGGING_LEVEL=info
PADDLE_RETRY_MAX_ATTEMPTS=3
PADDLE_RETRY_DELAY_MS=1000
PADDLE_CONNECT_TIMEOUT=10
PADDLE_REQUEST_TIMEOUT=30

# ShurjoPay Payment Gateway Configuration
SHURJOPAY_ENVIRONMENT=sandbox
SHURJOPAY_USERNAME=sp_sandbox
SHURJOPAY_PASSWORD=pyyk97hu&6u6
SHURJOPAY_PREFIX=NOK
SHURJOPAY_API_URL=https://sandbox.shurjopayment.com
SHURJOPAY_CALLBACK_URL=
SHURJOPAY_LOG_LOCATION=storage/logs/shurjopay
SHURJOPAY_SSL_VERIFY=true
SHURJOPAY_LOGGING_ENABLED=true
SHURJOPAY_LOGGING_CHANNEL=daily
SHURJOPAY_LOGGING_LEVEL=info
SHURJOPAY_CURRENCY=BDT
SHURJOPAY_DEBUG=false

# Coinbase Commerce Configuration
COINBASE_COMMERCE_API_KEY=
COINBASE_COMMERCE_WEBHOOK_SECRET=
COINBASE_COMMERCE_DEBUG=true
COINBASE_COMMERCE_AUTO_ACTIVATE=true
COINBASE_COMMERCE_AUTO_USDC_SETTLEMENT=true
COINBASE_COMMERCE_INSTANT_CONFIRMATION=true
