<?php

namespace Tests\Unit;

use App\Providers\ShurjoPayServiceProvider;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class ShurjoPayServiceProviderTest extends TestCase
{
    protected ShurjoPayServiceProvider $provider;

    protected function setUp(): void
    {
        parent::setUp();
        $this->provider = new ShurjoPayServiceProvider($this->app);
    }

    /** @test */
    public function service_provider_respects_logging_enabled_setting()
    {
        // Disable logging
        Config::set('shurjopay.logging.enabled', false);
        
        // Mock the Log facade
        Log::shouldReceive('channel')->never();
        Log::shouldReceive('info')->never();
        Log::shouldReceive('warning')->never();
        
        // Call the validateConfiguration method via reflection
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('validateConfiguration');
        $method->setAccessible(true);
        
        // This should not log anything when logging is disabled
        $method->invoke($this->provider);
        
        $this->assertTrue(true); // If we get here without Log::shouldReceive failing, the test passes
    }

    /** @test */
    public function service_provider_logs_when_logging_enabled()
    {
        // Enable logging
        Config::set('shurjopay.logging.enabled', true);
        Config::set('shurjopay.username', 'test_user');
        Config::set('shurjopay.password', 'test_password');
        Config::set('shurjopay.prefix', 'TEST');
        Config::set('shurjopay.environment', 'sandbox');
        
        // Mock the Log facade to expect logging
        Log::shouldReceive('channel')
            ->with('shurjopay')
            ->once()
            ->andReturnSelf();
        Log::shouldReceive('info')
            ->with('ShurjoPay configuration validation passed')
            ->once();
        
        // Call the validateConfiguration method via reflection
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('validateConfiguration');
        $method->setAccessible(true);
        
        // This should log when logging is enabled and config is valid
        $method->invoke($this->provider);
    }

    /** @test */
    public function service_provider_logs_errors_when_logging_enabled_and_config_invalid()
    {
        // Enable logging
        Config::set('shurjopay.logging.enabled', true);
        Config::set('shurjopay.username', ''); // Invalid - empty username
        Config::set('shurjopay.password', ''); // Invalid - empty password
        Config::set('shurjopay.prefix', ''); // Invalid - empty prefix
        Config::set('shurjopay.environment', 'invalid'); // Invalid environment
        
        // Mock the Log facade to expect error logging
        Log::shouldReceive('channel')
            ->with('shurjopay')
            ->once()
            ->andReturnSelf();
        Log::shouldReceive('warning')
            ->with('ShurjoPay configuration issues detected', \Mockery::type('array'))
            ->once();
        
        // Also expect individual error logs if app.debug is true
        Config::set('app.debug', true);
        Log::shouldReceive('warning')
            ->with(\Mockery::pattern('/ShurjoPay: .*/'))
            ->atLeast(1);
        
        // Call the validateConfiguration method via reflection
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('validateConfiguration');
        $method->setAccessible(true);
        
        // This should log errors when logging is enabled and config is invalid
        $method->invoke($this->provider);
    }

    /** @test */
    public function configure_logging_method_respects_logging_enabled_setting()
    {
        // Disable logging
        Config::set('shurjopay.logging.enabled', false);
        
        // Mock the Log facade - should not be called
        Log::shouldReceive('channel')->never();
        Log::shouldReceive('info')->never();
        
        // Call the configureLogging method via reflection
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('configureLogging');
        $method->setAccessible(true);
        
        // This should not configure logging when disabled
        $method->invoke($this->provider);
        
        $this->assertTrue(true); // If we get here without Log::shouldReceive failing, the test passes
    }

    /** @test */
    public function configure_logging_method_sets_up_logging_when_enabled()
    {
        // Enable logging
        Config::set('shurjopay.logging.enabled', true);
        Config::set('shurjopay.environment', 'sandbox');
        Config::set('shurjopay.debug', false);
        Config::set('shurjopay.logging.level', 'info');
        
        // Clear any existing shurjopay channel config
        Config::set('logging.channels.shurjopay', null);
        
        // Mock the Log facade to expect initialization logging
        Log::shouldReceive('channel')
            ->with('shurjopay')
            ->once()
            ->andReturnSelf();
        Log::shouldReceive('info')
            ->with('ShurjoPay service provider initialized', \Mockery::type('array'))
            ->once();
        
        // Call the configureLogging method via reflection
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('configureLogging');
        $method->setAccessible(true);
        
        // This should configure logging when enabled
        $method->invoke($this->provider);
        
        // Verify that the logging channel was configured
        $this->assertNotNull(config('logging.channels.shurjopay'));
        $this->assertEquals('daily', config('logging.channels.shurjopay.driver'));
    }

    /** @test */
    public function debug_information_method_respects_debug_and_app_debug_settings()
    {
        // Test with both app.debug and shurjopay.debug enabled
        Config::set('app.debug', true);
        Config::set('shurjopay.debug', true);
        
        // Mock the Log facade to expect debug logging
        Log::shouldReceive('channel')
            ->with('shurjopay')
            ->once()
            ->andReturnSelf();
        Log::shouldReceive('debug')
            ->with('ShurjoPay Debug Information', \Mockery::type('array'))
            ->once();
        
        // Call the addDebugInformation method via reflection
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('addDebugInformation');
        $method->setAccessible(true);
        
        $method->invoke($this->provider);
    }

    /** @test */
    public function debug_information_method_does_not_log_when_debug_disabled()
    {
        // Test with debug disabled
        Config::set('app.debug', false);
        Config::set('shurjopay.debug', false);
        
        // Mock the Log facade - should not be called
        Log::shouldReceive('channel')->never();
        Log::shouldReceive('debug')->never();
        
        // The addDebugInformation method should not be called when debug is disabled
        // This is tested implicitly by the boot method logic
        $this->assertTrue(true);
    }
}
