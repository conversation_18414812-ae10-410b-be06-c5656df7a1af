<?php

namespace App\Providers;

use App\Services\ShurjoPayService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class ShurjoPayServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register ShurjoPayService as singleton
        $this->app->singleton(ShurjoPayService::class, function ($app) {
            return new ShurjoPayService();
        });

        // Register configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/shurjopay.php', 'shurjopay'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Configure logging for ShurjoPay
        $this->configureLogging();

        // Add debug information in development
        if (config('app.debug') && config('shurjopay.debug')) {
            $this->addDebugInformation();
        }

        // Validate configuration in development
        if (app()->environment(['local', 'development'])) {
            $this->validateConfiguration();
        }
    }

    /**
     * Configure logging for ShurjoPay.
     */
    protected function configureLogging(): void
    {
        if (!config('shurjopay.logging.enabled')) {
            return;
        }

        // Ensure the shurjopay channel is configured
        if (!config('logging.channels.shurjopay')) {
            $logConfig = [
                'driver' => 'daily',
                'path' => storage_path('logs/shurjopay.log'),
                'level' => config('shurjopay.logging.level', 'info'),
                'days' => 14,
                'permission' => 0664,
            ];

            Config::set('logging.channels.shurjopay', $logConfig);
        }

        try {
            // Log service initialization
            Log::channel('shurjopay')->info('ShurjoPay service provider initialized', [
                'environment' => config('shurjopay.environment'),
                'debug_mode' => config('shurjopay.debug'),
                'logging_level' => config('shurjopay.logging.level'),
            ]);
        } catch (\Exception $e) {
            // Fallback to default log channel if shurjopay channel fails
            Log::info('ShurjoPay service provider initialized (fallback logging)', [
                'environment' => config('shurjopay.environment'),
                'debug_mode' => config('shurjopay.debug'),
                'logging_level' => config('shurjopay.logging.level'),
                'logging_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Add debug information for development.
     */
    protected function addDebugInformation(): void
    {
        try {
            Log::channel('shurjopay')->debug('ShurjoPay Debug Information', [
                'config' => [
                    'environment' => config('shurjopay.environment'),
                    'api_url' => config('shurjopay.api_url'),
                    'username' => config('shurjopay.username') ? '***configured***' : 'not_configured',
                    'password' => config('shurjopay.password') ? '***configured***' : 'not_configured',
                    'prefix' => config('shurjopay.prefix'),
                    'currency' => config('shurjopay.currency'),
                    'ssl_verify' => config('shurjopay.ssl_verify'),
                ],
                'urls' => config('shurjopay.urls'),
                'supported_currencies' => config('shurjopay.supported_currencies'),
            ]);
        } catch (\Exception $e) {
            // Fallback to default log channel if shurjopay channel fails
            Log::debug('ShurjoPay Debug Information (fallback logging)', [
                'config' => [
                    'environment' => config('shurjopay.environment'),
                    'api_url' => config('shurjopay.api_url'),
                    'username' => config('shurjopay.username') ? '***configured***' : 'not_configured',
                    'password' => config('shurjopay.password') ? '***configured***' : 'not_configured',
                    'prefix' => config('shurjopay.prefix'),
                    'currency' => config('shurjopay.currency'),
                    'ssl_verify' => config('shurjopay.ssl_verify'),
                ],
                'urls' => config('shurjopay.urls'),
                'supported_currencies' => config('shurjopay.supported_currencies'),
                'logging_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Validate configuration in development.
     */
    protected function validateConfiguration(): void
    {
        $errors = [];

        // Check required configuration
        if (!config('shurjopay.username')) {
            $errors[] = 'ShurjoPay username not configured';
        }

        if (!config('shurjopay.password')) {
            $errors[] = 'ShurjoPay password not configured';
        }

        if (!config('shurjopay.prefix')) {
            $errors[] = 'ShurjoPay prefix not configured';
        }

        if (!in_array(config('shurjopay.environment'), ['sandbox', 'production'])) {
            $errors[] = 'Invalid ShurjoPay environment configured';
        }

        // Only log if logging is enabled
        if (!config('shurjopay.logging.enabled')) {
            return;
        }

        if (!empty($errors)) {
            try {
                Log::channel('shurjopay')->warning('ShurjoPay configuration issues detected', [
                    'errors' => $errors,
                    'environment' => app()->environment(),
                ]);
            } catch (\Exception $e) {
                Log::warning('ShurjoPay configuration issues detected (fallback logging)', [
                    'errors' => $errors,
                    'environment' => app()->environment(),
                    'logging_error' => $e->getMessage(),
                ]);
            }

            if (config('app.debug')) {
                foreach ($errors as $error) {
                    Log::warning("ShurjoPay: {$error}");
                }
            }
        } else {
            try {
                Log::channel('shurjopay')->info('ShurjoPay configuration validation passed');
            } catch (\Exception $e) {
                Log::info('ShurjoPay configuration validation passed (fallback logging)', [
                    'logging_error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            ShurjoPayService::class,
        ];
    }
}
